import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import Binance from 'node-binance-api';

export async function POST(request: NextRequest) {
  try {
    let body;
    try {
      body = await request.json();
    } catch (parseError) {
      console.error("Erro ao fazer parse do JSON da requisição:", parseError);
      return NextResponse.json({ success: false, message: "Corpo da requisição inválido ou ausente." }, { status: 400 });
    }
    
    const { apiKey, apiSecret, symbol, positionSide } = body;

    if (!apiKey || !apiSecret) {
      return NextResponse.json({ success: false, message: "API Key ou Secret ausentes." }, { status: 400 });
    }

    if (!symbol) {
      return NextResponse.json({ success: false, message: "Símbolo é obrigatório." }, { status: 400 });
    }

    const binance = new Binance().options({
      APIKEY: apiKey,
      APISECRET: apiSecret,
      family: 4,
    });

    console.log(`========= CLOSE POSITION - INCOMING REQUEST =========`);
    console.log(`Symbol: ${symbol}`);
    console.log(`Position Side: ${positionSide || 'BOTH'}`);
    console.log(`====================================================`);

    // Get current position information
    const positions = await binance.futuresPositionRisk({ symbol });
    const currentPosition = positions.find((pos: any) => 
      pos.symbol === symbol && 
      (positionSide ? pos.positionSide === positionSide : parseFloat(pos.positionAmt) !== 0)
    );

    if (!currentPosition || parseFloat(currentPosition.positionAmt) === 0) {
      return NextResponse.json({ 
        success: false, 
        message: `Nenhuma posição aberta encontrada para ${symbol}` 
      }, { status: 400 });
    }

    const positionAmount = parseFloat(currentPosition.positionAmt);
    const isLongPosition = positionAmount > 0;
    const positionQuantity = Math.abs(positionAmount);

    // Cancel any existing stop/take profit orders first
    try {
      const openOrders = await binance.futuresOpenOrders({ symbol });
      const relatedOrders = openOrders.filter((order: any) => 
        (order.type === 'STOP_MARKET' || order.type === 'TAKE_PROFIT_MARKET') &&
        order.symbol === symbol
      );

      for (const order of relatedOrders) {
        try {
          await binance.futuresCancelOrder({
            symbol: order.symbol,
            orderId: order.orderId
          });
          console.log(`Cancelled related order ${order.orderId} (${order.type})`);
        } catch (cancelError: any) {
          console.warn(`Failed to cancel order ${order.orderId}:`, cancelError.message);
        }
      }
    } catch (orderError: any) {
      console.warn('Error checking/cancelling related orders:', orderError.message);
    }

    // Place market order to close the position
    try {
      const closeOrder = await binance.futuresOrder({
        symbol: symbol,
        side: isLongPosition ? 'SELL' : 'BUY', // Opposite side to close position
        type: 'MARKET',
        quantity: positionQuantity.toString(),
        reduceOnly: true
      });

      console.log(`Position close order placed successfully: ${closeOrder.orderId}`);

      return NextResponse.json({
        success: true,
        message: `Posição fechada com sucesso para ${symbol}`,
        data: {
          symbol,
          orderId: closeOrder.orderId,
          status: closeOrder.status,
          side: closeOrder.side,
          quantity: closeOrder.origQty,
          executedQuantity: closeOrder.executedQty,
          positionSide: currentPosition.positionSide || 'BOTH'
        }
      });

    } catch (closeError: any) {
      console.error('Error closing position:', closeError);
      return NextResponse.json({ 
        success: false, 
        message: `Erro ao fechar posição: ${closeError.body ? JSON.parse(closeError.body).msg : closeError.message}` 
      }, { status: 400 });
    }

  } catch (error: any) {
    console.error("Erro interno ao fechar posição:", error);
    
    if (error && error.body && typeof error.body === 'string') {
      try {
        const errorBody = JSON.parse(error.body);
        if (errorBody.msg) {
          return NextResponse.json({ 
            success: false, 
            message: `Erro da API Binance: ${errorBody.msg}` 
          }, { status: 401 });
        }
      } catch (parseError) { /* Ignore parse error, fall through to generic */ }
    }
    
    return NextResponse.json({ 
      success: false, 
      message: "Falha interna ao fechar posição." 
    }, { status: 500 });
  }
} 