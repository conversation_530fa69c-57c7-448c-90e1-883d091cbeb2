"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface FloatingNextJSBadgeClientProps {
  className?: string;
}

export function FloatingNextJSBadgeClient({ className }: FloatingNextJSBadgeClientProps) {
  const [isHovered, setIsHovered] = useState(false);

  const handleReload = () => {
    window.location.reload();
  };

  const clearCache = () => {
    localStorage.clear();
    sessionStorage.clear();
    if ('caches' in window) {
      caches.keys().then(names => {
        names.forEach(name => {
          caches.delete(name);
        });
      });
    }
    window.location.reload();
  };

  const openDevTools = () => {
    console.log('%c🛠️ Next.js Development Mode', 'font-size: 16px; color: #ef4444; font-weight: bold;');
    console.log('%c▲ Press F12 to open DevTools', 'font-size: 12px; color: #666;');
    console.log('%c🔄 Use Ctrl+R or Cmd+R for hot reload', 'font-size: 12px; color: #666;');
    if (typeof window !== 'undefined' && (window as any).__NEXT_DATA__) {
      console.log('%cNext.js Build Info:', 'font-weight: bold;', (window as any).__NEXT_DATA__);
    }
  };

  const handleScrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleScrollToBottom = () => {
    window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
  };

  return (
    <TooltipProvider>
      <div 
        className={cn(
          "fixed left-6 bottom-6 z-[9999] flex flex-col gap-2 items-start",
          "pointer-events-auto",
          className
        )}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        style={{ 
          left: '24px',
          bottom: '24px',
          zIndex: 9999,
          pointerEvents: 'auto'
        }}
      >
        {/* Menu expandido quando hover */}
        <div className={cn(
          "flex flex-col gap-2 transition-all duration-300 ease-out",
          isHovered ? "opacity-100 translate-y-0 scale-100" : "opacity-0 translate-y-4 scale-95 pointer-events-none"
        )}>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="secondary"
                size="sm"
                onClick={handleScrollToTop}
                className="bg-black/90 hover:bg-black text-white border border-red-400/30 backdrop-blur-md shadow-lg hover:shadow-red-400/20"
              >
                ⬆️ Top
              </Button>
            </TooltipTrigger>
            <TooltipContent side="right">
              Scroll para o Topo
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="secondary"
                size="sm"
                onClick={handleScrollToBottom}
                className="bg-black/90 hover:bg-black text-white border border-red-400/30 backdrop-blur-md shadow-lg hover:shadow-red-400/20"
              >
                ⬇️ Bottom
              </Button>
            </TooltipTrigger>
            <TooltipContent side="right">
              Scroll para o Final
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="secondary"
                size="sm"
                onClick={clearCache}
                className="bg-black/90 hover:bg-black text-white border border-red-400/30 backdrop-blur-md shadow-lg hover:shadow-red-400/20"
              >
                🧹 Clear
              </Button>
            </TooltipTrigger>
            <TooltipContent side="right">
              Limpar Cache & Reload
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="secondary"
                size="sm"
                onClick={handleReload}
                className="bg-black/90 hover:bg-black text-white border border-red-400/30 backdrop-blur-md shadow-lg hover:shadow-red-400/20"
              >
                🔄 Reload
              </Button>
            </TooltipTrigger>
            <TooltipContent side="right">
              Recarregar Página
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="secondary"
                size="sm"
                onClick={openDevTools}
                className="bg-black/90 hover:bg-black text-white border border-blue-400/30 backdrop-blur-md shadow-lg hover:shadow-blue-400/20"
              >
                🛠️ Dev
              </Button>
            </TooltipTrigger>
            <TooltipContent side="right">
              Ferramentas de Desenvolvimento
            </TooltipContent>
          </Tooltip>
        </div>

        {/* Badge principal com símbolo do Next.js */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Badge 
              variant="secondary"
              className={cn(
                "h-14 w-14 rounded-full cursor-pointer transition-all duration-300",
                "bg-gradient-to-br from-red-600 to-red-800 hover:from-red-500 hover:to-red-700",
                "text-white border-2 border-red-400/50 hover:border-red-300/70",
                "flex items-center justify-center text-lg font-bold",
                "shadow-xl hover:shadow-2xl hover:shadow-red-500/25 backdrop-blur-md",
                "hover:scale-110 active:scale-95",
                "ring-0 hover:ring-4 ring-red-400/30",
                isHovered && "ring-4 ring-red-400/50 shadow-red-500/30"
              )}
              style={{
                boxShadow: isHovered 
                  ? '0 20px 25px -5px rgba(239, 68, 68, 0.3), 0 10px 10px -5px rgba(239, 68, 68, 0.2)' 
                  : '0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2)'
              }}
            >
              <span className="text-2xl font-extrabold select-none tracking-tight">N</span>
            </Badge>
          </TooltipTrigger>
          <TooltipContent side="right">
            <div className="text-center">
              <div className="font-semibold">Next.js 15.2.3</div>
              <div className="text-xs text-muted-foreground">Development Mode</div>
              <div className="text-xs text-red-400 mt-1">Hover para ferramentas</div>
            </div>
          </TooltipContent>
        </Tooltip>
      </div>
    </TooltipProvider>
  );
} 