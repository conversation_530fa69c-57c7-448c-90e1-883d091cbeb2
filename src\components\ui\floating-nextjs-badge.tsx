"use client";

import dynamic from 'next/dynamic';

interface FloatingNextJSBadgeProps {
  className?: string;
}

// Dynamic import para evitar problemas de hidratação
const FloatingNextJSBadgeClient = dynamic(() => 
  import('./floating-nextjs-badge-client').then(mod => ({ default: mod.FloatingNextJSBadgeClient })), 
  { 
    ssr: false,
    loading: () => null
  }
);

export function FloatingNextJSBadge({ className }: FloatingNextJSBadgeProps) {
  // Só mostrar em desenvolvimento
  if (process.env.NODE_ENV !== 'development') return null;

  return <FloatingNextJSBadgeClient className={className} />;
}