
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import Binance from 'node-binance-api';
import type { PortfolioAsset } from '@/lib/types';

interface AssetBalance {
  asset: string;
  free: string;
  locked: string;
}

interface FuturesBalance {
  asset: string;
  balance: string;
  availableBalance?: string; 
}

export async function POST(request: NextRequest) {
  try {
    let body;
    try {
      const textBody = await request.text();
      if (!textBody || textBody.trim() === '') {
        return NextResponse.json({ success: false, message: "Corpo da requisição ausente. API Key e Secret são obrigatórios." }, { status: 400 });
      }
      body = JSON.parse(textBody);
    } catch (parseError) {
      console.error("Erro ao fazer parse do JSON da requisição:", parseError);
      return NextResponse.json({ success: false, message: "Corpo da requisição inválido. Certifique-se de enviar JSON válido com apiKey e apiSecret." }, { status: 400 });
    }
    
    const { apiKey: bodyApiKey, apiSecret: bodyApiSecret } = body;

    // Use API keys from request body or fallback to environment variables
    const apiKey = bodyApiKey || process.env.BINANCE_API_KEY;
    const apiSecret = bodyApiSecret || process.env.BINANCE_API_SECRET;

    if (!apiKey || !apiSecret) {
      return NextResponse.json({
        success: false,
        message: "API Key ou Secret ausentes. Forneça as chaves na requisição ou configure as variáveis de ambiente BINANCE_API_KEY e BINANCE_API_SECRET."
      }, { status: 400 });
    }

    const binance = new Binance().options({
      APIKEY: apiKey,
      APISECRET: apiSecret,
      family: 4, 
    });

    // 1. Get Spot Account Info
    let accountInfo;
    try {
      accountInfo = await binance.account();
    } catch (e: any) {
      console.error("Erro ao buscar informações da conta Spot Binance:", e.body || e.message || e);
      const errorMsg = e.body ? JSON.parse(e.body).msg : e.message;
      return NextResponse.json({ success: false, message: `Erro da API Binance (Conta Spot): ${errorMsg || 'Não foi possível obter informações da conta spot.'}` }, { status: 500 });
    }
    
    const spotBalances: AssetBalance[] = accountInfo.balances.filter((b: AssetBalance) => parseFloat(b.free) > 0 || parseFloat(b.locked) > 0);
    
    let totalSpotValueUSDT = 0;
    let totalSpotValueYesterdayUSDT = 0;
    const spotAssets: PortfolioAsset[] = [];

    // Fetch all prices and 24-hour ticker statistics once
    let allPrices: any = {};
    try {
      allPrices = await binance.prices();
    } catch (e: any) {
      console.error("Erro ao buscar preços da Binance:", e.body || e.message || e);
      // Do not fail the whole request if prices fail, assets can still be listed without price
    }

    let allTicker24hrStats: any[] = [];
    try {
      const raw24hrStats = await binance.prevDay(); // Fetches for ALL symbols
      if (Array.isArray(raw24hrStats)) {
        allTicker24hrStats = raw24hrStats;
      } else {
        console.error("Erro ao buscar 24h stats: Resposta da API não é um array.", raw24hrStats);
      }
    } catch (e: any) {
        console.error("Erro CRÍTICO ao buscar 24h stats da Binance:", e.body || e.message || e);
        // allTicker24hrStats remains [], leading to 0% change for assets if this fails
    }

    if (spotBalances.length > 0) {
      for (const balance of spotBalances) {
        const quantity = parseFloat(balance.free) + parseFloat(balance.locked);
        if (quantity === 0) continue;

        let price = 0;
        let tradingPair = '';
        
        if (allPrices[balance.asset + 'USDT']) {
          price = parseFloat(allPrices[balance.asset + 'USDT']);
          tradingPair = balance.asset + 'USDT';
        } else if (allPrices[balance.asset + 'BUSD']) { // BUSD is deprecated but might still appear in old accounts
          price = parseFloat(allPrices[balance.asset + 'BUSD']);
          tradingPair = balance.asset + 'BUSD';
        }
        
        // For stablecoins, assume price is 1 USD
        if (['USDT', 'BUSD', 'USDC', 'DAI', 'TUSD', 'PAX'].includes(balance.asset.toUpperCase())) {
          price = 1; 
          tradingPair = balance.asset; // No specific trading pair needed for 24h change lookup
        }

        // Skip assets that are known to be problematic or non-tradeable
        const problematicAssets = ['LDBTC', 'LDUSDC', 'LDSHIB2', 'ETHW', 'LDPEPE', 'MONKY', 'AKRO'];
        const isProblematicAsset = problematicAssets.includes(balance.asset.toUpperCase());
        
        if (price === 0 && quantity > 0 && !['USDT', 'BUSD', 'USDC', 'DAI', 'TUSD', 'PAX'].includes(balance.asset.toUpperCase()) ) {
          if (!isProblematicAsset) {
            console.warn(`Preço não encontrado para ${balance.asset} contra USDT/BUSD. Listando com valor 0.`);
          }
          spotAssets.push({
            id: balance.asset + '_spot',
            name: balance.asset,
            symbol: balance.asset,
            quantity: quantity,
            price: 0,
            value: 0,
            change24h: 0, // Defaults to 0 if no price found
            exchange: 'Binance Spot',
          });
          continue; 
        }

        const value = quantity * price;
        totalSpotValueUSDT += value;

        let change24h = 0;
        if (!['USDT', 'BUSD', 'USDC', 'DAI', 'TUSD', 'PAX'].includes(balance.asset.toUpperCase()) && tradingPair) {
            const stat = allTicker24hrStats.find((s: any) => s.symbol === tradingPair);
            if (stat && stat.priceChangePercent !== undefined && stat.priceChangePercent !== null) {
              const parsedChange = parseFloat(String(stat.priceChangePercent));
              if (!isNaN(parsedChange)) {
                change24h = parsedChange;
              }
            }
        }
        
        const valueYesterday = change24h === -100 && value > 0 ? value / 0.00000001 : value / (1 + (change24h / 100));
        if(!isNaN(valueYesterday) && isFinite(valueYesterday)){
           totalSpotValueYesterdayUSDT += valueYesterday;
        } else if (change24h === -100 && value === 0) {
            // If current value is 0 and it dropped 100%, yesterday's value was also effectively 0 for P&L
        }


        spotAssets.push({
          id: balance.asset + '_spot',
          name: balance.asset,
          symbol: balance.asset,
          quantity: quantity,
          price: price,
          value: value,
          change24h: change24h,
          exchange: 'Binance Spot',
        });
      }
    }

    // 2. Get Futures Wallet Balance (for stablecoins like USDT)
    let totalFuturesWalletUSDT = 0;
    try {
      const futuresBalances: FuturesBalance[] = await binance.futuresBalance();
      if (futuresBalances && Array.isArray(futuresBalances)) {
        const usdtFuturesBalance = futuresBalances.find(fb => fb.asset === 'USDT');
        if (usdtFuturesBalance && usdtFuturesBalance.balance) {
          totalFuturesWalletUSDT = parseFloat(usdtFuturesBalance.balance);
        }
      }
    } catch (e: any) {
      console.warn("Aviso ao buscar saldo da carteira de futuros Binance:", e.body || e.message || e);
    }

    // 3. Combine Spot and Futures for Total Portfolio Value
    const totalPortfolioValueUSDT = totalSpotValueUSDT + totalFuturesWalletUSDT;

    // 4. Calculate Spot P&L
    const spotPnl24hUSDT = totalSpotValueUSDT - totalSpotValueYesterdayUSDT;
    const spotPnl24hPercentage = totalSpotValueYesterdayUSDT !== 0 ? (spotPnl24hUSDT / totalSpotValueYesterdayUSDT) * 100 : (totalSpotValueUSDT > 0 ? 0 : 0); 
    
    spotAssets.sort((a, b) => b.value - a.value);

    if (totalFuturesWalletUSDT > 0) {
        spotAssets.push({
            id: 'USDT_futures_wallet', // Unique ID for futures wallet balance
            name: 'USDT (Futures Wallet)',
            symbol: 'USDT',
            quantity: totalFuturesWalletUSDT,
            price: 1, // USDT price is 1
            value: totalFuturesWalletUSDT,
            change24h: 0, // No 24h change for wallet balance itself
            exchange: 'Binance Futures Wallet',
        });
        // Re-sort if futures wallet USDT is added
        spotAssets.sort((a, b) => b.value - a.value);
    }


    return NextResponse.json({
      success: true,
      data: {
        accountOverviewData: { 
            totalPortfolioValueUSDT: parseFloat(totalPortfolioValueUSDT.toFixed(2)),
            totalSpotValueUSDT: parseFloat(totalSpotValueUSDT.toFixed(2)), 
            totalFuturesValueUSDT: parseFloat(totalFuturesWalletUSDT.toFixed(2)),
            spotPnl24hUSDT: parseFloat(spotPnl24hUSDT.toFixed(2)),
            spotPnl24hPercentage: parseFloat(spotPnl24hPercentage.toFixed(2)),
            assets: spotAssets,
            allPrices: allPrices // Pass all fetched prices to the client
        }
      }
    });

  } catch (error: any) {
    console.error("Erro interno na rota account-overview:", error);
    return NextResponse.json({ success: false, message: "Falha interna ao buscar visão geral da conta." }, { status: 500 });
  }
}

    