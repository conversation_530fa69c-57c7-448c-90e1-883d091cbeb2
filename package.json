{"name": "nextn", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 9002", "build": "next build", "build:production": "node scripts/build-production.js", "start": "next start", "start:production": "NODE_ENV=production next start -p 3000", "lint": "next lint", "lint:fix": "next lint --fix", "typecheck": "tsc --noEmit", "health-check": "node scripts/health-check.js", "test:api": "node scripts/health-check.js", "perf:local": "lighthouse http://localhost:9002 --output=html --output-path=./reports/lighthouse-local.html --view", "perf:prod": "lighthouse https://cryptopilot-jn2omabg6-breno-miguez-lima-dias-da-silvas-projects.vercel.app --output=html --output-path=./reports/lighthouse-prod.html --view", "test:performance": "npm run perf:local && npm run perf:prod"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack-query-firebase/react": "^1.0.5", "@tanstack/react-query": "^5.66.0", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "crypto": "npm:crypto-browserify", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "firebase": "^11.8.1", "lucide-react": "^0.475.0", "next": "^15.3.3", "node-binance-api": "^1.0.10", "patch-package": "^8.0.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "recharts": "^2.15.1", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@fullhuman/postcss-purgecss": "^7.0.2", "@next/bundle-analyzer": "^15.3.3", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "cssnano": "^7.0.7", "eslint": "9.28.0", "eslint-config-next": "15.3.3", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "5.8.3"}}