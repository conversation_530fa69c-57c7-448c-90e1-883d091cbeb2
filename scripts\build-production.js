#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 CryptoPilot - Production Build Script');
console.log('========================================');

// Check if package.json exists
if (!fs.existsSync('package.json')) {
  console.error('❌ package.json not found. Make sure you are in the project root.');
  process.exit(1);
}

try {
  console.log('📦 Installing dependencies...');
  execSync('npm ci --only=production', { stdio: 'inherit' });

  console.log('🔍 Type checking...');
  execSync('npx tsc --noEmit', { stdio: 'inherit' });

  console.log('🧹 Linting code...');
  execSync('npx eslint . --ext .ts,.tsx --fix', { stdio: 'inherit' });

  console.log('🏗️ Building application...');
  execSync('npm run build', { stdio: 'inherit' });

  console.log('✅ Production build completed successfully!');
  console.log('');
  console.log('🔧 Next steps:');
  console.log('1. Configure your Binance API keys in the application');
  console.log('2. Set up environment variables for production');
  console.log('3. Deploy using: npm start');
  console.log('');
  console.log('📋 Build artifacts:');
  console.log('- .next/ - Built application');
  console.log('- public/ - Static assets');
  
} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
} 