
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import Binance from 'node-binance-api';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { apiKey: bodyApiKey, apiSecret: bodyApiSecret } = body;

    // Use API keys from request body or fallback to environment variables
    const apiKey = bodyApiKey || process.env.BINANCE_API_KEY;
    const apiSecret = bodyApiSecret || process.env.BINANCE_API_SECRET;

    if (!apiKey || !apiSecret) {
      return NextResponse.json({
        success: false,
        message: "API Key ou Secret ausentes. Forneça as chaves na requisição ou configure as variáveis de ambiente BINANCE_API_KEY e BINANCE_API_SECRET."
      }, { status: 400 });
    }

    const binance = new Binance().options({
      APIKEY: apiKey,
      APISECRET: apiSecret,
    });

    try {
      const prices = await binance.prices('BTCUSDT');
      if (prices && prices.BTCUSDT) {
        // Ensure prices.BTCUSDT is treated as string since the API returns string values
        const priceString = String(prices.BTCUSDT);
        const currentBTCPrice = parseFloat(priceString).toLocaleString(undefined, { style: 'currency', currency: 'USD' });
        return NextResponse.json({ success: true, price: currentBTCPrice });
      } else {
        return NextResponse.json({ success: false, message: "Não foi possível obter o preço do BTCUSDT." }, { status: 500 });
      }
    } catch (priceFetchError: any) {
      console.error("Erro ao buscar preço do BTCUSDT em tempo real:", priceFetchError?.body || priceFetchError?.message || priceFetchError);
      let errorMessage = "Falha ao buscar preço do BTCUSDT em tempo real.";
       if (priceFetchError && typeof priceFetchError.body === 'string') {
        try {
            const errorBody = JSON.parse(priceFetchError.body);
            errorMessage = `Erro da Binance ao buscar preço: ${errorBody.msg} (Código: ${errorBody.code})`;
        } catch (parseError) {
            errorMessage = `Erro da Binance ao buscar preço: ${priceFetchError.body}`;
        }
      } else if (priceFetchError && priceFetchError.message) {
        errorMessage = priceFetchError.message;
      }
      return NextResponse.json({ success: false, message: errorMessage }, { status: 500 });
    }
  } catch (error: any) {
    console.error("Erro interno ao processar busca de preço em tempo real:", error);
    return NextResponse.json({ success: false, message: "Falha interna ao buscar preço em tempo real." }, { status: 500 });
  }
}
