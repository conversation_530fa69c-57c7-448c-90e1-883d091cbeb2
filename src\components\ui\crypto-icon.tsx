"use client";

import { useState } from "react";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { Coins, DollarSign } from "lucide-react";

interface CryptoIconProps {
  symbol: string;
  size?: number;
  className?: string;
  showFallback?: boolean;
}

const CRYPTO_ICON_URLS = {
  primary: (symbol: string) => `https://cryptoicons.org/api/icon/${symbol.toLowerCase()}/200`,
  fallback: (symbol: string) => `https://assets.coincap.io/assets/icons/${symbol.toLowerCase()}@2x.png`,
  backup: (symbol: string) => `https://cdn.jsdelivr.net/gh/atomiclabs/cryptocurrency-icons@1a63530be6e374711a8554f31b17e4cb92c25fa5/128/icon/${symbol.toLowerCase()}.png`,
};

const WELL_KNOWN_CRYPTOS = {
  'BTC': { name: 'Bitcoin', color: '#F7931A' },
  'ETH': { name: 'Ethereum', color: '#627EEA' },
  'BNB': { name: 'Binance Coin', color: '#F3BA2F' },
  'ADA': { name: 'Cardano', color: '#0033AD' },
  'SOL': { name: 'Solana', color: '#9945FF' },
  'DOT': { name: 'Polkadot', color: '#E6007A' },
  'MATIC': { name: 'Polygon', color: '#8247E5' },
  'AVAX': { name: 'Avalanche', color: '#E84142' },
  'LINK': { name: 'Chainlink', color: '#375BD2' },
  'UNI': { name: 'Uniswap', color: '#FF007A' },
  'USDT': { name: 'Tether', color: '#26A17B' },
  'USDC': { name: 'USD Coin', color: '#2775CA' },
  'BUSD': { name: 'Binance USD', color: '#F0B90B' },
  'XRP': { name: 'Ripple', color: '#23292F' },
  'DOGE': { name: 'Dogecoin', color: '#C2A633' },
  'SHIB': { name: 'Shiba Inu', color: '#FFA409' },
  'LTC': { name: 'Litecoin', color: '#BFBBBB' },
  'BCH': { name: 'Bitcoin Cash', color: '#8DC351' },
  'ATOM': { name: 'Cosmos', color: '#2E3148' },
  'ALGO': { name: 'Algorand', color: '#000000' },
};

const FallbackIcon = ({ symbol, size, className }: { symbol: string, size: number, className?: string }) => {
  const crypto = WELL_KNOWN_CRYPTOS[symbol.toUpperCase() as keyof typeof WELL_KNOWN_CRYPTOS];
  
  return (
    <div 
      className={cn(
        "flex items-center justify-center rounded-full border-2 font-bold text-white",
        className
      )}
      style={{ 
        width: size, 
        height: size, 
        backgroundColor: crypto?.color || '#6B7280',
        borderColor: crypto?.color || '#6B7280',
        fontSize: size * 0.4,
      }}
    >
      {symbol.slice(0, 2).toUpperCase()}
    </div>
  );
};

const LoadingIcon = ({ size, className }: { size: number, className?: string }) => (
  <div 
    className={cn(
      "flex items-center justify-center rounded-full bg-muted border animate-pulse",
      className
    )}
    style={{ width: size, height: size }}
  >
    <Coins 
      className="text-muted-foreground" 
      style={{ width: size * 0.5, height: size * 0.5 }} 
    />
  </div>
);

export function CryptoIcon({ 
  symbol, 
  size = 40, 
  className, 
  showFallback = true 
}: CryptoIconProps) {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [currentUrlIndex, setCurrentUrlIndex] = useState(0);

  const handleImageError = () => {
    const urls = Object.values(CRYPTO_ICON_URLS);
    
    if (currentUrlIndex < urls.length - 1) {
      setCurrentUrlIndex(currentUrlIndex + 1);
      setIsLoading(true);
    } else {
      setImageError(true);
      setIsLoading(false);
    }
  };

  const handleImageLoad = () => {
    setIsLoading(false);
    setImageError(false);
  };

  // Special handling for stablecoins and common tokens
  if (symbol.toUpperCase() === 'USDT' || symbol.toUpperCase() === 'USDC' || symbol.toUpperCase() === 'BUSD') {
    return <FallbackIcon symbol={symbol} size={size} className={className} />;
  }

  if (isLoading) {
    return <LoadingIcon size={size} className={className} />;
  }

  if (imageError || !showFallback) {
    return <FallbackIcon symbol={symbol} size={size} className={className} />;
  }

  const urls = Object.values(CRYPTO_ICON_URLS);
  const currentUrl = urls[currentUrlIndex](symbol);

  return (
    <div className={cn("relative overflow-hidden rounded-full", className)}>
      <Image
        src={currentUrl}
        alt={`${symbol} icon`}
        width={size}
        height={size}
        className="rounded-full object-cover bg-white"
        onError={handleImageError}
        onLoad={handleImageLoad}
        unoptimized
        priority={false}
      />
      
      {/* Overlay for better contrast */}
      <div className="absolute inset-0 rounded-full ring-1 ring-black/10 dark:ring-white/10" />
    </div>
  );
}

// Higher-level component for crypto assets with additional info
export function CryptoAssetIcon({ 
  symbol, 
  name, 
  size = 40, 
  showName = false, 
  className 
}: CryptoIconProps & { 
  name?: string; 
  showName?: boolean; 
}) {
  return (
    <div className={cn("flex items-center gap-3", className)}>
      <CryptoIcon symbol={symbol} size={size} />
      
      {showName && (
        <div className="min-w-0 flex-1">
          <div className="font-medium text-sm truncate">{name || symbol}</div>
          <div className="text-xs text-muted-foreground font-mono">
            {symbol.toUpperCase()}
          </div>
        </div>
      )}
    </div>
  );
}

// Compact version for smaller spaces
export function CryptoIconMini({ symbol, className }: { symbol: string; className?: string }) {
  return (
    <CryptoIcon 
      symbol={symbol} 
      size={20} 
      className={cn("shrink-0", className)} 
      showFallback={true} 
    />
  );
} 