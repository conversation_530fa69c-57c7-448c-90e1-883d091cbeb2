#!/usr/bin/env node

const http = require('http');
const https = require('https');

const APP_URL = process.env.NEXT_PUBLIC_APP_URL || process.argv[2] || 'http://localhost:3000';
const TIMEOUT = 10000; // 10 seconds

console.log('🏥 CryptoPilot - Health Check');
console.log('============================');
console.log(`Checking: ${APP_URL}`);

function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const client = url.startsWith('https') ? https : http;
    
    const req = client.get(url, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: data
        });
      });
    });

    req.on('error', reject);
    req.setTimeout(TIMEOUT, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

async function checkHealth() {
  try {
    console.log('🔍 Checking main application...');
    const mainResponse = await makeRequest(APP_URL);
    
    if (mainResponse.statusCode === 200) {
      console.log('✅ Main application is healthy');
    } else {
      console.log(`⚠️ Main application returned status: ${mainResponse.statusCode}`);
    }

    console.log('🔍 Checking API endpoints...');
    
    // Check test connection endpoint
    const testResponse = await makeRequest(`${APP_URL}/api/binance/test-connection`);
    if (testResponse.statusCode === 200) {
      console.log('✅ Test connection endpoint is healthy');
    } else {
      console.log(`⚠️ Test connection endpoint returned status: ${testResponse.statusCode}`);
    }

    // Check exchange info endpoint
    const exchangeResponse = await makeRequest(`${APP_URL}/api/binance/exchange-info`);
    if (exchangeResponse.statusCode === 200) {
      console.log('✅ Exchange info endpoint is healthy');
    } else {
      console.log(`⚠️ Exchange info endpoint returned status: ${exchangeResponse.statusCode}`);
    }

    console.log('');
    console.log('🎉 Health check completed!');
    console.log('Application appears to be running correctly.');
    
  } catch (error) {
    console.error('❌ Health check failed:', error.message);
    process.exit(1);
  }
}

checkHealth(); 